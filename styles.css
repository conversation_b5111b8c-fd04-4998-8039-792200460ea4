/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #ffffff;
    background-color: #0a0a0a;
    overflow-x: hidden;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(20px);
    z-index: 1000;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo h2 {
    color: #ffffff;
    font-weight: 700;
    font-size: 1.5rem;
    letter-spacing: -0.5px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2.5rem;
    align-items: center;
}

.nav-item {
    position: relative;
}

.nav-link {
    text-decoration: none;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-link:hover,
.nav-link.active {
    color: #ff6b35;
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -8px;
    left: 0;
    background: linear-gradient(90deg, #ff6b35, #f7931e);
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* Dropdown Menu */
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1rem 0;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    list-style: none;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    margin: 0;
}

.dropdown-menu a {
    display: block;
    padding: 0.75rem 1.5rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.dropdown-menu a:hover {
    color: #ff6b35;
    background: rgba(255, 107, 53, 0.1);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1001;
}

.hamburger:hover {
    background: rgba(255, 107, 53, 0.1);
}

.bar {
    width: 25px;
    height: 3px;
    background-color: #ffffff;
    margin: 3px 0;
    transition: all 0.3s ease;
    border-radius: 2px;
}

/* Hamburger Animation */
.hamburger.active .bar:nth-child(1) {
    transform: rotate(-45deg) translate(-6px, 6px);
    background-color: #ff6b35;
}

.hamburger.active .bar:nth-child(2) {
    opacity: 0;
}

.hamburger.active .bar:nth-child(3) {
    transform: rotate(45deg) translate(-6px, -6px);
    background-color: #ff6b35;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    overflow: hidden;
    background: #0a0a0a;
}

.hero-slideshow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.slide.active {
    opacity: 1;
}

.slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 100%);
    z-index: 2;
}

.hero-content {
    position: relative;
    z-index: 3;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-text {
    animation: fadeInLeft 1s ease-out;
}

.hero-subtitle {
    color: #ff6b35;
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    letter-spacing: 0.5px;
}

.hero-title {
    font-size: 4rem;
    font-weight: 800;
    color: white;
    margin-bottom: 1.5rem;
    line-height: 1.1;
    letter-spacing: -2px;
}

.hero-description {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
    line-height: 1.6;
    max-width: 500px;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2.5rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: #ff6b35;
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.6);
    margin-top: 0.25rem;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    padding: 14px 32px;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    display: inline-block;
    text-align: center;
    letter-spacing: 0.5px;
}

.btn-primary {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.4);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* Camera Floating Elements */
.camera-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 2;
}

.camera-float {
    position: absolute;
    color: rgba(255, 107, 53, 0.3);
    font-size: 2rem;
    animation: float 6s ease-in-out infinite;
}

.camera-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.camera-2 {
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.camera-3 {
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
}

.lens-flare {
    position: absolute;
    background: radial-gradient(circle, rgba(255, 107, 53, 0.4) 0%, transparent 70%);
    border-radius: 50%;
    animation: flare 4s ease-in-out infinite;
}

.lens-1 {
    width: 100px;
    height: 100px;
    top: 15%;
    right: 20%;
    animation-delay: 1s;
}

.lens-2 {
    width: 60px;
    height: 60px;
    bottom: 40%;
    right: 10%;
    animation-delay: 3s;
}

.lens-3 {
    width: 80px;
    height: 80px;
    top: 50%;
    left: 5%;
    animation-delay: 5s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
    25% { transform: translateY(-20px) rotate(5deg); opacity: 0.6; }
    50% { transform: translateY(-10px) rotate(-5deg); opacity: 0.4; }
    75% { transform: translateY(-15px) rotate(3deg); opacity: 0.5; }
}

@keyframes flare {
    0%, 100% { opacity: 0; transform: scale(1); }
    50% { opacity: 0.6; transform: scale(1.2); }
}

.hero-subtitle i {
    margin-right: 0.5rem;
    color: #ff6b35;
    animation: pulse 2s infinite;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2.5rem;
}

.stat {
    text-align: center;
    position: relative;
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.5rem;
    color: white;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.stat:hover .stat-icon {
    transform: scale(1.1) rotate(10deg);
    box-shadow: 0 10px 25px rgba(255, 107, 53, 0.4);
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    padding: 14px 32px;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-align: center;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.4);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.hero-gallery {
    animation: fadeInRight 1s ease-out;
}

.gallery-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.gallery-item {
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
    cursor: pointer;
}

.gallery-item:hover {
    transform: scale(1.05) rotate(2deg);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.gallery-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: all 0.3s ease;
}

.gallery-item:hover img {
    filter: brightness(1.1) contrast(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.8), rgba(0, 0, 0, 0.6));
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    opacity: 0;
    transition: all 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-overlay i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    animation: bounce 1s infinite;
}

.gallery-overlay span {
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* Camera Flash Effect */
.camera-flash-effect {
    position: fixed;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, rgba(255, 107, 53, 0.6) 30%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    transform: translate(-50%, -50%) scale(0);
    animation: cameraFlashEffect 0.6s ease-out forwards;
}

@keyframes cameraFlashEffect {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
    }
    50% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) scale(4);
        opacity: 0;
    }
}

/* Enhanced Camera Lens Effect */
.service-card::after {
    content: '';
    position: absolute;
    top: 10px;
    right: 10px;
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.8), rgba(247, 147, 30, 0.8));
    border-radius: 50%;
    opacity: 0;
    transform: scale(0);
    transition: all 0.3s ease;
    z-index: 10;
}

.service-card:hover::after {
    opacity: 1;
    transform: scale(1);
    animation: lensGlint 1s ease-in-out;
}

@keyframes lensGlint {
    0%, 100% { box-shadow: 0 0 0 0 rgba(255, 107, 53, 0.4); }
    50% { box-shadow: 0 0 0 10px rgba(255, 107, 53, 0); }
}

/* Camera Viewfinder Effect */
.hero::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    height: 200px;
    border: 2px solid rgba(255, 107, 53, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    animation: viewfinder 8s ease-in-out infinite;
    pointer-events: none;
    z-index: 3;
}

@keyframes viewfinder {
    0%, 90%, 100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(1);
    }
    5%, 15% {
        opacity: 0.6;
        transform: translate(-50%, -50%) scale(1.2);
    }
    10% {
        opacity: 0.8;
        transform: translate(-50%, -50%) scale(1.1);
    }
}

.slideshow-controls {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 0.5rem;
    z-index: 4;
}

.slide-btn {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    background: rgba(255, 255, 255, 0.4);
    cursor: pointer;
    transition: all 0.3s ease;
}

.slide-btn.active {
    background: #ff6b35;
    transform: scale(1.2);
}

/* Page Header */
.page-header {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    text-align: center;
}

.page-header h1 {
    font-size: 3rem;
    font-weight: 800;
    color: white;
    margin-bottom: 1rem;
    letter-spacing: -1px;
}

.page-header p {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.7);
    max-width: 600px;
    margin: 0 auto;
}

/* Section Styles */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
    animation: fadeInUp 0.8s ease-out;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1rem;
    position: relative;
    letter-spacing: -1px;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #ff6b35, #f7931e);
}

.section-subtitle {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.7);
    max-width: 600px;
    margin: 0 auto;
}

/* Featured Work Section */
.featured-work {
    padding: 100px 0;
    background: #0a0a0a;
}

.featured-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 1.5rem;
    height: 600px;
}

.featured-item {
    position: relative;
    border-radius: 16px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.featured-item:hover {
    transform: scale(1.02);
}

.featured-item.large {
    grid-row: 1 / 3;
}

.featured-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.featured-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
    color: white;
    padding: 2rem;
    transform: translateY(20px);
    opacity: 0;
    transition: all 0.3s ease;
}

.featured-item:hover .featured-overlay {
    transform: translateY(0);
    opacity: 1;
}

.featured-overlay .category {
    color: #ff6b35;
    font-size: 0.9rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.5rem;
    display: block;
}

.featured-overlay h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.featured-overlay p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1rem;
    line-height: 1.5;
}

.view-more {
    color: #ff6b35;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: color 0.3s ease;
}

.view-more:hover {
    color: #f7931e;
}

/* Testimonials Section */
.testimonials {
    padding: 100px 0;
    background: #111111;
    text-align: center;
}

.testimonial-content {
    max-width: 800px;
    margin: 0 auto;
}

.quote-icon {
    font-size: 3rem;
    color: #ff6b35;
    margin-bottom: 2rem;
}

.testimonials blockquote {
    font-size: 1.8rem;
    font-weight: 300;
    color: white;
    line-height: 1.6;
    margin-bottom: 2rem;
    font-style: italic;
}

.testimonial-author h4 {
    color: white;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.testimonial-author p {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
}

/* Portfolio Categories */
.portfolio-categories {
    padding: 100px 0;
    background: #0a0a0a;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.category-card {
    position: relative;
    border-radius: 16px;
    overflow: hidden;
    text-decoration: none;
    color: white;
    transition: transform 0.3s ease;
    height: 300px;
}

.category-card:hover {
    transform: translateY(-10px);
}

.category-card.coming-soon {
    opacity: 0.7;
}

.category-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.category-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.category-card:hover .category-image img {
    transform: scale(1.1);
}

.category-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
    padding: 2rem;
    z-index: 2;
}

.category-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.category-content p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1rem;
    line-height: 1.5;
}

.category-count {
    color: #ff6b35;
    font-size: 0.9rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.category-overlay {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    background: rgba(255, 107, 53, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.3s ease;
    z-index: 3;
}

.category-card:hover .category-overlay {
    opacity: 1;
    transform: scale(1);
}

.coming-soon-text {
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Gallery Styles */
.featured-gallery {
    padding: 100px 0;
    background: #111111;
}

.gallery-filter {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 12px 24px;
    background: transparent;
    border: 2px solid rgba(255, 107, 53, 0.3);
    color: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-btn.active,
.filter-btn:hover {
    background: #ff6b35;
    border-color: #ff6b35;
    color: white;
    transform: translateY(-2px);
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.gallery-item {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 300px;
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
    color: white;
    padding: 1.5rem;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
    transform: translateY(0);
}

.gallery-overlay h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.gallery-overlay p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.gallery-actions {
    display: flex;
    gap: 0.5rem;
}

.view-btn,
.like-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-btn:hover,
.like-btn:hover {
    background: #ff6b35;
    transform: scale(1.1);
}

/* Contact Section */
.contact-section {
    padding: 100px 0;
    background: #0a0a0a;
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-info h2 {
    font-size: 2.2rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1.5rem;
    line-height: 1.3;
}

.contact-info > p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 3rem;
    line-height: 1.6;
    font-size: 1.1rem;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 3rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.contact-text h4 {
    color: white;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.contact-text p {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 0.5rem;
}

.contact-link {
    color: #ff6b35;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.contact-link:hover {
    color: #f7931e;
}

.whatsapp-link {
    background: #25d366;
    color: white !important;
    padding: 8px 16px;
    border-radius: 20px;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.whatsapp-link:hover {
    background: #128c7e;
    transform: translateY(-2px);
}

.social-section {
    margin-top: 2rem;
}

.social-section h4 {
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.social-links {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
    font-size: 0.95rem;
}

.social-link:hover {
    color: #ff6b35;
}

.social-link i {
    width: 24px;
    font-size: 1.2rem;
}

/* Contact Form */
.contact-form-container {
    background: #111111;
    padding: 2.5rem;
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.form-header {
    margin-bottom: 2rem;
}

.form-header h3 {
    color: white;
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.form-header p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.95rem;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    color: white;
    font-weight: 500;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 12px 16px;
    color: white;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    font-family: 'Inter', sans-serif;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #ff6b35;
    background: rgba(255, 255, 255, 0.08);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.submit-btn {
    background: linear-gradient(135deg, #25d366, #128c7e);
    color: white;
    padding: 16px 32px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(37, 211, 102, 0.3);
}

.form-note {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
    text-align: center;
    margin-top: 0.5rem;
}

/* Map Section */
.map-section {
    padding: 100px 0;
    background: #0a0a0a;
}

.map-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

.map-info h3 {
    color: white;
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.map-info p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.location-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.95rem;
}

.feature-item i {
    color: #ff6b35;
    font-size: 1.1rem;
    width: 20px;
}

.map-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.map-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 12px 20px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    text-align: center;
}

.map-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(255, 107, 53, 0.4);
}

.map-btn.whatsapp {
    background: linear-gradient(135deg, #25d366, #128c7e);
}

.map-btn.whatsapp:hover {
    box-shadow: 0 10px 25px rgba(37, 211, 102, 0.4);
}

.map-embed {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* FAQ Section */
.faq-section {
    padding: 100px 0;
    background: #111111;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.faq-item {
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.faq-item:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-5px);
}

.faq-item h4 {
    color: white;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.faq-item p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
}

/* CTA Section */
.cta-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.cta-content p {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.cta-section .btn-primary {
    background: white;
    color: #ff6b35;
    border: 2px solid white;
}

.cta-section .btn-primary:hover {
    background: transparent;
    color: white;
    border-color: white;
}

.cta-section .btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.5);
}

.cta-section .btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: white;
}

/* About Page Specific Styles */
.about-content {
    padding: 100px 0;
    background: #0a0a0a;
}

.about-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.about-text .lead {
    font-size: 1.3rem;
    color: #ff6b35;
    font-weight: 500;
    margin-bottom: 2rem;
    line-height: 1.5;
}

.about-text p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1.5rem;
    line-height: 1.7;
    font-size: 1.05rem;
}

.skills {
    margin-top: 3rem;
}

.skills h3 {
    color: white;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.skill-list {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.skill-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.skill-item i {
    color: #ff6b35;
    font-size: 1.1rem;
}

.about-image {
    position: relative;
}

.about-image img {
    width: 100%;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.image-overlay {
    position: absolute;
    top: 20px;
    right: 20px;
}

.experience-badge {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.3);
}

.experience-badge .number {
    display: block;
    font-size: 1.8rem;
    font-weight: 700;
    line-height: 1;
}

.experience-badge .text {
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.9;
}

/* Stats Section */
.stats-section {
    padding: 100px 0;
    background: #111111;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.stat-card {
    text-align: center;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.stat-card:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 2.5rem;
    color: #ff6b35;
    margin-bottom: 1rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    display: block;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Philosophy Section - Removed duplicate */

.philosophy-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.quote-section {
    text-align: center;
}

.quote-section .quote-icon {
    font-size: 3rem;
    color: #ff6b35;
    margin-bottom: 2rem;
}

.quote-section blockquote {
    font-size: 1.5rem;
    font-weight: 300;
    color: white;
    line-height: 1.6;
    margin-bottom: 2rem;
    font-style: italic;
}

.quote-section cite {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    font-style: normal;
}

.philosophy-text h3 {
    color: white;
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.philosophy-text p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

/* Equipment Section */
.equipment-section {
    padding: 100px 0;
    background: #111111;
}

.equipment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.equipment-item {
    text-align: center;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.equipment-item:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-5px);
}

.equipment-item i {
    font-size: 2.5rem;
    color: #ff6b35;
    margin-bottom: 1rem;
}

.equipment-item h4 {
    color: white;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.equipment-item p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Footer */
.footer {
    background: #000000;
    color: white;
    padding: 60px 0 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1.5rem;
    color: white;
    font-weight: 600;
}

.footer-section h3 {
    font-size: 1.5rem;
}

.footer-section h4 {
    font-size: 1.1rem;
}

.footer-section p {
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.75rem;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
    font-size: 0.95rem;
}

.footer-section ul li a:hover {
    color: #ff6b35;
}

.footer .social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.footer .social-links a {
    width: 45px;
    height: 45px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 1.2rem;
}

.footer .social-links a:hover {
    background: #ff6b35;
    transform: translateY(-2px);
}

/* Enhanced Footer Contact Info */
.contact-info-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    margin-bottom: 1rem;
    padding: 0.5rem 0;
    transition: all 0.3s ease;
}

.contact-info-item:hover {
    transform: translateX(5px);
}

.contact-info-item i {
    color: #ff6b35;
    font-size: 1.1rem;
    margin-top: 0.2rem;
    min-width: 20px;
}

.contact-info-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
    line-height: 1.4;
}

.contact-info-item a:hover {
    color: #ff6b35;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
}

/* Animations */
@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Scroll Animations */
.animate-element {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
}

.animate-element.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Loading States */
body {
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

body.loaded {
    opacity: 1;
}

.page-transition {
    opacity: 0;
    transition: opacity 0.3s ease-out;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .hero-title {
        font-size: 3rem;
    }

    .featured-grid {
        grid-template-columns: 1fr 1fr;
        grid-template-rows: auto auto auto;
        height: auto;
    }

    .featured-item.large {
        grid-row: 1 / 2;
        grid-column: 1 / 3;
        height: 300px;
    }

    .about-grid,
    .contact-grid,
    .philosophy-content,
    .map-container,
    .contact-content,
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-brand {
        text-align: center;
    }

    .social-links {
        justify-content: center;
        flex-direction: row;
        flex-wrap: nowrap;
    }
}

@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 0;
        flex-direction: column;
        background: linear-gradient(135deg, rgba(10, 10, 10, 0.98), rgba(20, 20, 20, 0.98));
        backdrop-filter: blur(20px);
        width: 100%;
        height: 100vh;
        text-align: center;
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.5);
        padding: 120px 0 2rem;
        border-right: 1px solid rgba(255, 107, 53, 0.2);
        overflow-y: auto;
        z-index: 1000;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-item {
        margin: 1.5rem 0;
        opacity: 0;
        transform: translateX(-30px);
        transition: all 0.3s ease;
    }

    .nav-menu.active .nav-item {
        opacity: 1;
        transform: translateX(0);
    }

    .nav-menu.active .nav-item:nth-child(1) { transition-delay: 0.1s; }
    .nav-menu.active .nav-item:nth-child(2) { transition-delay: 0.2s; }
    .nav-menu.active .nav-item:nth-child(3) { transition-delay: 0.3s; }
    .nav-menu.active .nav-item:nth-child(4) { transition-delay: 0.4s; }

    .nav-link {
        font-size: 1.2rem;
        padding: 1rem 2rem;
        border-radius: 12px;
        margin: 0 1rem;
        display: block;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .nav-link:hover,
    .nav-link.active {
        background: rgba(255, 107, 53, 0.1);
        color: #ff6b35;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(255, 107, 53, 0.2);
    }

    .nav-link::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 107, 53, 0.1), transparent);
        transition: left 0.5s ease;
    }

    .nav-link:hover::before {
        left: 100%;
    }

    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        background: rgba(255, 107, 53, 0.05);
        margin: 1rem;
        border-radius: 12px;
        border: 1px solid rgba(255, 107, 53, 0.2);
        backdrop-filter: blur(10px);
        max-height: 0;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .nav-item.dropdown:hover .dropdown-menu,
    .nav-item.dropdown.active .dropdown-menu {
        max-height: 300px;
        padding: 1rem 0;
    }

    .dropdown-menu li {
        margin: 0.5rem 0;
    }

    .dropdown-menu a {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
        border-radius: 8px;
        margin: 0 1rem;
        transition: all 0.3s ease;
    }

    .dropdown-menu a:hover {
        background: rgba(255, 107, 53, 0.15);
        transform: translateX(10px);
    }

    .hero {
        min-height: 80vh;
        padding: 100px 20px 50px;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        justify-content: center;
        gap: 1.5rem;
    }

    .page-header h1 {
        font-size: 2.5rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .featured-grid {
        grid-template-columns: 1fr;
        height: auto;
    }

    .featured-item.large {
        grid-row: auto;
        grid-column: auto;
        height: 250px;
    }

    .categories-grid {
        grid-template-columns: 1fr;
    }

    .gallery-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .equipment-grid {
        grid-template-columns: 1fr;
    }

    .faq-grid {
        grid-template-columns: 1fr;
    }

    .skill-list {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .cta-content h2 {
        font-size: 2rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    /* About Page Mobile Styles */
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .intro-grid,
    .philosophy-grid {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .intro-features {
        flex-direction: column;
        gap: 1rem;
    }

    .skills-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .equipment-grid {
        grid-template-columns: 1fr;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .cta-contact-quick {
        flex-direction: column;
        gap: 1rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .cta-content h2 {
        font-size: 2rem;
    }

    .contact-methods {
        gap: 1rem;
    }

    .contact-method {
        padding: 0.75rem;
    }

    .method-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .form-footer {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .page-header h1 {
        font-size: 2rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .about-text h2 {
        font-size: 2rem;
    }

    .contact-info h2 {
        font-size: 1.8rem;
    }

    .contact-form-container {
        padding: 1.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .testimonials blockquote {
        font-size: 1.3rem;
    }

    .cta-content h2 {
        font-size: 1.8rem;
    }
}

/* Navbar Scroll Effect */
.navbar.scrolled {
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(20px);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
    background: #ff6b35;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #f7931e;
}

/* Camera Cursor Effects */
.service-card,
.gallery-item,
.portfolio-item {
    cursor: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="%23ff6b35"><path d="M12 15.5c-1.93 0-3.5-1.57-3.5-3.5s1.57-3.5 3.5-3.5 3.5 1.57 3.5 3.5-1.57 3.5-3.5 3.5zm7-11h-3.17L14 2H10L8.17 4.5H5c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-12c0-1.1-.9-2-2-2z"/></svg>') 16 16, pointer;
}

/* Camera Shutter Animation */
@keyframes shutterClick {
    0% { transform: scale(1); }
    50% { transform: scale(0.95); }
    100% { transform: scale(1); }
}

.service-card:active,
.gallery-item:active {
    animation: shutterClick 0.2s ease-out;
}

/* Camera Lens Border Effect */
.service-image,
.gallery-item,
.portfolio-item {
    position: relative;
}

.service-image::before,
.gallery-item::before,
.portfolio-item::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #ff6b35, #f7931e, #ff6b35, #f7931e);
    background-size: 400% 400%;
    border-radius: inherit;
    opacity: 0;
    z-index: -1;
    transition: opacity 0.3s ease;
    animation: gradientShift 3s ease infinite;
}

.service-card:hover .service-image::before,
.gallery-item:hover::before,
.portfolio-item:hover::before {
    opacity: 1;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Camera Focus Ring Effect */
.hero-gallery .gallery-item {
    position: relative;
}

.hero-gallery .gallery-item::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 60px;
    height: 60px;
    border: 2px solid rgba(255, 107, 53, 0.8);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: none;
}

.hero-gallery .gallery-item:hover::after {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
    animation: focusRing 1s ease-in-out;
}

@keyframes focusRing {
    0%, 100% {
        border-color: rgba(255, 107, 53, 0.8);
        box-shadow: 0 0 0 0 rgba(255, 107, 53, 0.4);
    }
    50% {
        border-color: rgba(255, 107, 53, 1);
        box-shadow: 0 0 0 10px rgba(255, 107, 53, 0);
    }
}

/* Camera Body Styling for Navigation */
.navbar {
    background: linear-gradient(135deg, rgba(10, 10, 10, 0.95), rgba(20, 20, 20, 0.95));
    border-bottom: 1px solid rgba(255, 107, 53, 0.2);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
}

.nav-logo h2::before {
    content: '📷 ';
    margin-right: 0.5rem;
}

/* Camera Settings Display */
.hero-stats .stat::before {
    content: '';
    position: absolute;
    top: -5px;
    right: -5px;
    width: 8px;
    height: 8px;
    background: #ff6b35;
    border-radius: 50%;
    animation: recordingLight 2s ease-in-out infinite;
}

@keyframes recordingLight {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 1; }
}

/* Selection */
::selection {
    background: #ff6b35;
    color: white;
}

::-moz-selection {
    background: #ff6b35;
    color: white;
}

/* Wedding Gallery Styles */
.wedding-gallery {
    padding: 100px 0;
    background: #0a0a0a;
}

.gallery-intro {
    text-align: center;
    margin-bottom: 4rem;
}

.gallery-intro h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1.5rem;
    line-height: 1.3;
}

.gallery-intro p {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.8);
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.6;
}

.wedding-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.wedding-item {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 300px;
}

.wedding-item.large {
    grid-column: span 2;
    height: 400px;
}

.wedding-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.wedding-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.wedding-item:hover img {
    transform: scale(1.1);
}

.wedding-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
    color: white;
    padding: 2rem;
    transform: translateY(20px);
    opacity: 0;
    transition: all 0.3s ease;
}

.wedding-item:hover .wedding-overlay {
    transform: translateY(0);
    opacity: 1;
}

.wedding-overlay h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.wedding-overlay p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.5rem;
}

.wedding-date {
    color: #ff6b35;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Wedding Services */
.wedding-services {
    padding: 100px 0;
    background: #111111;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.service-item {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(255,107,53,0.1);
    overflow: hidden;
}

.service-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0,0,0,0.15);
}

.service-image {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.service-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255,107,53,0.8) 0%, rgba(247,147,30,0.8) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.service-item:hover .service-image img {
    transform: scale(1.1);
}

.service-item:hover .service-overlay {
    opacity: 1;
}

.service-content {
    padding: 2rem;
    text-align: center;
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    transition: transform 0.3s ease;
}

.service-item:hover .service-icon {
    transform: scale(1.1);
}

.service-item h3 {
    color: #333;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.service-item p {
    color: #666;
    line-height: 1.6;
}

/* Nature Gallery Styles */
.nature-gallery {
    padding: 120px 0;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
}

.gallery-intro {
    text-align: center;
    margin-bottom: 80px;
}

.intro-content {
    max-width: 800px;
    margin: 0 auto;
}

.gallery-intro .section-badge {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    color: white;
    padding: 12px 24px;
    border-radius: 30px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 30px;
}

.gallery-intro h2 {
    font-size: 3.5rem;
    font-weight: 900;
    color: white;
    margin-bottom: 25px;
    background: linear-gradient(135deg, #ffffff 0%, #ff6b35 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.gallery-intro p {
    font-size: 1.2rem;
    color: rgba(255,255,255,0.8);
    line-height: 1.8;
    margin-bottom: 40px;
}

.intro-features {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(255,255,255,0.05);
    padding: 15px 20px;
    border-radius: 25px;
    color: rgba(255,255,255,0.9);
    font-weight: 600;
    border: 1px solid rgba(255,107,53,0.2);
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: rgba(255,107,53,0.1);
    border-color: rgba(255,107,53,0.4);
    transform: translateY(-2px);
}

.feature-item i {
    color: #ff6b35;
    font-size: 1.1rem;
}

.nature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.nature-item {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 40px rgba(0,0,0,0.3);
    transition: all 0.4s ease;
    cursor: pointer;
}

.nature-item.large {
    grid-column: span 2;
    height: 400px;
}

.nature-item:not(.large) {
    height: 300px;
}

.nature-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.nature-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        45deg,
        rgba(0,0,0,0.7) 0%,
        rgba(0,0,0,0.3) 50%,
        rgba(255,107,53,0.3) 100%
    );
    display: flex;
    align-items: flex-end;
    padding: 30px;
    opacity: 0;
    transition: all 0.4s ease;
}

.nature-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 60px rgba(255,107,53,0.2);
}

.nature-item:hover img {
    transform: scale(1.1);
}

.nature-item:hover .nature-overlay {
    opacity: 1;
}

.overlay-content h3 {
    color: white;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 8px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.overlay-content p {
    color: rgba(255,255,255,0.9);
    font-size: 1rem;
    margin-bottom: 10px;
}

.nature-date {
    color: #ff6b35;
    font-size: 0.9rem;
    font-weight: 600;
    background: rgba(255,255,255,0.1);
    padding: 5px 15px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.nature-services {
    padding: 120px 0;
    background: #f8f9fa;
}

/* Nature Header Styles */
.nature-header {
    position: relative;
    height: 100vh;
    min-height: 600px;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.header-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.header-background img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.header-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(0,0,0,0.6) 0%,
        rgba(0,0,0,0.3) 50%,
        rgba(255,107,53,0.2) 100%
    );
}

.nature-header .container {
    position: relative;
    z-index: 2;
}

.header-content {
    text-align: center;
    color: white;
    max-width: 800px;
    margin: 0 auto;
}

.header-badge {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: rgba(255,107,53,0.9);
    color: white;
    padding: 12px 24px;
    border-radius: 30px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 30px;
    backdrop-filter: blur(10px);
}

.nature-header h1 {
    font-size: 4rem;
    font-weight: 900;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    background: linear-gradient(135deg, #ffffff 0%, #ff6b35 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nature-header p {
    font-size: 1.3rem;
    margin-bottom: 50px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    color: rgba(255,255,255,0.9);
}

.header-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-top: 40px;
}

.header-stats .stat-item {
    text-align: center;
    background: rgba(255,255,255,0.1);
    padding: 20px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.header-stats .stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 900;
    color: #ff6b35;
    line-height: 1;
    margin-bottom: 5px;
}

.header-stats .stat-label {
    font-size: 0.9rem;
    color: rgba(255,255,255,0.8);
    font-weight: 600;
}

/* Testimonial Section */
.testimonial-section {
    padding: 100px 0;
    background: #0a0a0a;
}

.testimonial-card {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
    background: rgba(255, 255, 255, 0.05);
    padding: 3rem;
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.testimonial-card .quote-icon {
    font-size: 3rem;
    color: #ff6b35;
    margin-bottom: 2rem;
}

.testimonial-card blockquote {
    font-size: 1.5rem;
    font-weight: 300;
    color: white;
    line-height: 1.6;
    margin-bottom: 2rem;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.testimonial-author img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.author-info h4 {
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.author-info p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

/* Wedding Packages */
.wedding-packages {
    padding: 100px 0;
    background: #111111;
}

.packages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.package-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    padding: 2.5rem;
    text-align: center;
    border: 2px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.package-card:hover {
    transform: translateY(-10px);
    border-color: rgba(255, 107, 53, 0.5);
}

.package-card.featured {
    border-color: #ff6b35;
    transform: scale(1.05);
}

.popular-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.package-card h3 {
    color: white;
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.package-price {
    margin-bottom: 2rem;
}

.package-price .currency {
    font-size: 1.5rem;
    color: #ff6b35;
    font-weight: 600;
}

.package-price .amount {
    font-size: 3rem;
    color: #ff6b35;
    font-weight: 700;
}

.package-features {
    list-style: none;
    margin: 2rem 0;
    text-align: left;
}

.package-features li {
    padding: 0.75rem 0;
    color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.package-features .fa-check {
    color: #27ae60;
}

.package-features .fa-times {
    color: #e74c3c;
}

.package-btn {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    padding: 12px 30px;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-block;
    margin-top: 1rem;
}

.package-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.4);
}

/* Section Styles */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
    animation: fadeInUp 0.8s ease-out;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: #e74c3c;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* About Section */
.about {
    padding: 100px 0;
    background: #f8f9fa;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h3 {
    font-size: 2rem;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.about-text p {
    margin-bottom: 1.5rem;
    color: #666;
    line-height: 1.8;
}

.stats {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.stat {
    text-align: center;
}

.stat h4 {
    font-size: 2rem;
    color: #e74c3c;
    font-weight: 700;
}

.stat p {
    color: #666;
    margin: 0;
}

.about-image img {
    width: 100%;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.about-image img:hover {
    transform: scale(1.05);
}

/* Camera Decoration */
.camera-decoration {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 2rem;
}

.camera-decoration i {
    font-size: 2rem;
    color: #ff6b35;
    animation: cameraFloat 3s ease-in-out infinite;
}

.camera-flash {
    position: absolute;
    width: 20px;
    height: 20px;
    background: #ff6b35;
    border-radius: 50%;
    top: -5px;
    right: -5px;
    animation: cameraFlash 2s ease-in-out infinite;
    opacity: 0;
}

@keyframes cameraFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes cameraFlash {
    0%, 90%, 100% { opacity: 0; transform: scale(1); }
    5%, 10% { opacity: 1; transform: scale(1.2); }
}

/* Services Section */
.services {
    padding: 100px 0;
    background: #111111;
    position: relative;
    overflow: hidden;
}

.services::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="camera-pattern" x="0" y="0" width="50" height="50" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="2" fill="%23ff6b35" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23camera-pattern)"/></svg>');
    opacity: 0.3;
    z-index: 1;
}

.services .container {
    position: relative;
    z-index: 2;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2.5rem;
}

.service-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    transform: translateY(20px);
    opacity: 0;
    animation: slideInUp 0.8s ease-out forwards;
}

.service-card:nth-child(1) { animation-delay: 0.1s; }
.service-card:nth-child(2) { animation-delay: 0.2s; }
.service-card:nth-child(3) { animation-delay: 0.3s; }
.service-card:nth-child(4) { animation-delay: 0.4s; }
.service-card:nth-child(5) { animation-delay: 0.5s; }
.service-card:nth-child(6) { animation-delay: 0.6s; }

@keyframes slideInUp {
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(247, 147, 30, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.service-card:hover::before {
    opacity: 1;
}

.service-card:hover {
    transform: translateY(-15px) scale(1.02);
    border-color: rgba(255, 107, 53, 0.3);
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 107, 53, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.service-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
    filter: brightness(0.8);
}

.service-card:hover .service-image img {
    transform: scale(1.1);
    filter: brightness(1);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(255, 107, 53, 0.3));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.service-card:hover .image-overlay {
    opacity: 1;
}

.camera-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 107, 53, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    transform: scale(0.8);
    transition: all 0.3s ease;
    animation: pulse 2s infinite;
}

.service-card:hover .camera-icon {
    transform: scale(1);
    animation: none;
}

@keyframes pulse {
    0%, 100% { transform: scale(0.8); }
    50% { transform: scale(0.9); }
}

.service-content {
    padding: 2rem;
    position: relative;
    z-index: 2;
}

.service-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.service-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.service-card:hover .service-icon::before {
    left: 100%;
}

.service-card:hover .service-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 10px 25px rgba(255, 107, 53, 0.4);
}

.service-icon i {
    font-size: 1.5rem;
    color: white;
    z-index: 1;
    position: relative;
}

.service-card h3 {
    font-size: 1.4rem;
    color: white;
    margin-bottom: 1rem;
    font-weight: 600;
    line-height: 1.3;
}

.service-card p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1.5rem;
    line-height: 1.6;
    font-size: 0.95rem;
}

.service-features {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.service-features span {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.service-features i {
    color: #27ae60;
    font-size: 0.8rem;
}

.service-btn {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    padding: 12px 24px;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.service-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.service-btn:hover::before {
    left: 100%;
}

.service-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(255, 107, 53, 0.4);
}

.service-btn i {
    transition: transform 0.3s ease;
}

.service-btn:hover i {
    transform: translateX(3px);
}

/* Portfolio Section */
.portfolio {
    padding: 100px 0;
    background: #f8f9fa;
}

.portfolio-filter {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 10px 25px;
    background: white;
    border: 2px solid #e74c3c;
    color: #e74c3c;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.filter-btn.active,
.filter-btn:hover {
    background: #e74c3c;
    color: white;
    transform: translateY(-2px);
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.portfolio-item {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.portfolio-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.portfolio-item img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.portfolio-item:hover img {
    transform: scale(1.1);
}

.portfolio-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 2rem;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.portfolio-item:hover .portfolio-overlay {
    transform: translateY(0);
}

.portfolio-overlay h3 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
}

.portfolio-overlay p {
    opacity: 0.9;
}

/* Animations */
@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Pricing Section */
.pricing {
    padding: 100px 0;
    background: white;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.pricing-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    border: 2px solid transparent;
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.pricing-card.featured {
    border-color: #e74c3c;
    transform: scale(1.05);
}

.featured-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: #e74c3c;
    color: white;
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.pricing-header h3 {
    font-size: 1.8rem;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.price {
    margin: 1.5rem 0;
}

.currency {
    font-size: 1.5rem;
    color: #e74c3c;
    font-weight: 600;
}

.amount {
    font-size: 3rem;
    color: #e74c3c;
    font-weight: 700;
}

.pricing-header p {
    color: #666;
    margin-bottom: 2rem;
}

.pricing-features {
    list-style: none;
    margin: 2rem 0;
    text-align: left;
}

.pricing-features li {
    padding: 0.5rem 0;
    color: #666;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pricing-features .fa-check {
    color: #27ae60;
}

.pricing-features .fa-times {
    color: #e74c3c;
}

.pricing-btn {
    background: #e74c3c;
    color: white;
    padding: 12px 30px;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-block;
    margin-top: 1rem;
}

.pricing-btn:hover {
    background: #c0392b;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
}

/* Contact Section - Home Page */
.contact {
    padding: 100px 0;
    background: #0a0a0a;
    position: relative;
}

.contact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="contact-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23ff6b35" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23contact-pattern)"/></svg>');
    opacity: 0.3;
    z-index: 1;
}

.contact .container {
    position: relative;
    z-index: 2;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

/* Contact Info Card */
.contact-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2.5rem;
    transition: all 0.3s ease;
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 107, 53, 0.3);
}

.contact-header h3 {
    font-size: 1.8rem;
    color: white;
    margin-bottom: 1rem;
    font-weight: 600;
}

.contact-header p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
    line-height: 1.6;
    font-size: 1.05rem;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.contact-method:hover {
    background: rgba(255, 107, 53, 0.1);
    border-color: rgba(255, 107, 53, 0.3);
    transform: translateX(5px);
}

.method-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.method-icon.whatsapp {
    background: linear-gradient(135deg, #25d366, #128c7e);
}

.method-icon.email {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.method-icon.location {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.method-content h4 {
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.method-content a {
    color: #ff6b35;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.method-content a:hover {
    color: #f7931e;
}

.method-content span {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
    display: block;
    margin-top: 0.25rem;
}

.social-connect {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 1.5rem;
}

.social-connect h4 {
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.social-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 10px 16px;
    background: linear-gradient(135deg, #e1306c, #fd1d1d);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.social-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(225, 48, 108, 0.4);
}

/* Contact Form Section */
.form-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2.5rem;
    transition: all 0.3s ease;
}

.form-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 107, 53, 0.3);
}

.form-header h3 {
    font-size: 1.8rem;
    color: white;
    margin-bottom: 1rem;
    font-weight: 600;
}

.form-header p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.whatsapp-form-container {
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 1.5rem;
}

.form-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.security-badge,
.response-time {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.85rem;
}

.security-badge i,
.response-time i {
    color: #27ae60;
}

/* About Page Styles */

/* About Hero Section */
.about-hero {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-bg-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: brightness(0.3) contrast(1.1);
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(26,26,26,0.8) 0%, rgba(255,107,53,0.2) 50%, rgba(26,26,26,0.8) 100%);
    z-index: 2;
}

.hero-content {
    position: relative;
    z-index: 3;
    text-align: center;
    color: white;
    max-width: 900px;
    padding: 0 2rem;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.hero-title {
    font-size: 5rem;
    font-weight: 900;
    margin-bottom: 2rem;
    background: linear-gradient(135deg, #ffffff 0%, #ff6b35 50%, #f7931e 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 20px rgba(255,107,53,0.3);
    letter-spacing: -2px;
    line-height: 1.1;
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 4rem;
    color: rgba(255,255,255,0.9);
    line-height: 1.6;
    font-weight: 400;
    max-width: 700px;
    text-shadow: 0 2px 10px rgba(0,0,0,0.5);
}

.hero-scroll {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    color: #ff6b35;
    font-size: 1rem;
    font-weight: 600;
    animation: bounce 2s infinite;
    cursor: pointer;
    transition: all 0.3s ease;
}

.hero-scroll:hover {
    transform: translateY(-5px);
    color: #f7931e;
}

.hero-scroll i {
    font-size: 1.5rem;
    color: #ff6b35;
    background: rgba(255,107,53,0.1);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid rgba(255,107,53,0.3);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Philosophy Section - Bootstrap Based */
.philosophy-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
    position: relative;
}

.philosophy-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 30% 20%, rgba(255,107,53,0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(247,147,30,0.1) 0%, transparent 50%);
    z-index: 1;
}

.philosophy-section .container {
    position: relative;
    z-index: 2;
}

.philosophy-section .section-title {
    font-size: 3.5rem;
    font-weight: 800;
    color: #ffffff;
    margin-bottom: 15px;
    background: linear-gradient(135deg, #ffffff 0%, #ff6b35 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.philosophy-section .section-subtitle {
    font-size: 1.2rem;
    color: rgba(255,255,255,0.7);
    margin-bottom: 0;
}

.philosophy-quote-container {
    text-align: center;
    margin: 60px 0 80px 0;
}

.quote-box {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    color: white;
    padding: 40px;
    border-radius: 20px;
    max-width: 800px;
    margin: 0 auto;
    box-shadow: 0 15px 40px rgba(255,107,53,0.2);
    position: relative;
}

.quote-icon {
    font-size: 2rem;
    opacity: 0.3;
    margin-bottom: 20px;
}

.quote-text {
    font-size: 1.8rem;
    font-style: italic;
    font-weight: 600;
    line-height: 1.4;
    margin: 0;
}

/* Text Content */
.philosophy-text-content {
    padding-right: 30px;
}

.content-title {
    font-size: 2.2rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 25px;
    background: linear-gradient(135deg, #ffffff 0%, #ff6b35 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.content-description {
    font-size: 1.1rem;
    color: rgba(255,255,255,0.8);
    line-height: 1.8;
    margin-bottom: 40px;
}

/* Philosophy Principles */
.philosophy-principles {
    margin-top: 20px;
}

.principle-card {
    display: flex;
    align-items: flex-start;
    gap: 25px;
    padding: 30px;
    background: rgba(255,255,255,0.05);
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
    border-left: 4px solid #ff6b35;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
}

.principle-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255,107,53,0.3);
    background: rgba(255,255,255,0.08);
}

.principle-number {
    font-size: 2.5rem;
    font-weight: 900;
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
    min-width: 60px;
    flex-shrink: 0;
}

.principle-info h4 {
    font-size: 1.4rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 10px;
}

.principle-info p {
    color: rgba(255,255,255,0.7);
    line-height: 1.6;
    margin: 0;
    font-size: 1rem;
}

/* Image Section */
.philosophy-image-container {
    padding-left: 30px;
    position: relative;
}

.philosophy-img {
    width: 100%;
    height: 450px;
    object-fit: cover;
    border-radius: 20px;
    box-shadow: 0 15px 40px rgba(0,0,0,0.1);
}

/* Philosophy Stats Cards */
.philosophy-section .stats-cards {
    display: flex;
    gap: 15px;
    margin-top: 30px;
    justify-content: center;
}

.philosophy-section .stat-card {
    background: rgba(255,255,255,0.95) !important;
    padding: 25px !important;
    border-radius: 15px !important;
    box-shadow: 0 15px 40px rgba(0,0,0,0.3) !important;
    display: flex !important;
    align-items: center !important;
    gap: 15px !important;
    transition: all 0.3s ease !important;
    border: 2px solid rgba(255,107,53,0.3) !important;
    flex: 1 !important;
    backdrop-filter: blur(10px) !important;
    text-align: left !important;
    position: relative !important;
    z-index: 3 !important;
}

.philosophy-section .stat-card:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 20px 50px rgba(255,107,53,0.4) !important;
    background: rgba(255,255,255,1) !important;
    border-color: rgba(255,107,53,0.6) !important;
}

.philosophy-section .stat-icon {
    width: 50px !important;
    height: 50px !important;
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%) !important;
    border-radius: 12px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: white !important;
    font-size: 1.3rem !important;
    flex-shrink: 0 !important;
}

.philosophy-section .stat-content {
    display: flex !important;
    flex-direction: column !important;
}

.philosophy-section .stat-number {
    font-size: 1.8rem !important;
    font-weight: 800 !important;
    color: #1a1a1a !important;
    line-height: 1 !important;
    margin-bottom: 5px !important;
}

.philosophy-section .stat-label {
    font-size: 0.9rem !important;
    color: #666 !important;
    font-weight: 600 !important;
}



/* About Introduction */
.about-intro {
    padding: 120px 0;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
}

.intro-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.intro-image {
    position: relative;
}

.image-wrapper {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0,0,0,0.1);
}

.main-image {
    width: 100%;
    height: 600px;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.image-wrapper:hover .main-image {
    transform: scale(1.05);
}

.floating-card {
    position: absolute;
    bottom: 30px;
    right: 30px;
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 15px 40px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.card-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.3rem;
}

.card-content {
    display: flex;
    flex-direction: column;
}

.card-number {
    font-size: 1.8rem;
    font-weight: 800;
    color: #ffffff;
    line-height: 1;
}

.card-text {
    font-size: 0.85rem;
    color: rgba(255,255,255,0.8);
    font-weight: 500;
}

.image-decoration {
    position: absolute;
    top: -20px;
    left: -20px;
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    border-radius: 50%;
    opacity: 0.1;
    z-index: -1;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255,107,53,0.1);
    color: #ff6b35;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 600;
    margin-bottom: 1rem;
    border: 1px solid rgba(255,107,53,0.2);
}

.section-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: #ffffff;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.lead-text {
    font-size: 1.2rem;
    color: rgba(255,255,255,0.8);
    line-height: 1.7;
    margin-bottom: 2rem;
    font-weight: 500;
}

.story-text p {
    color: rgba(255,255,255,0.7);
    line-height: 1.8;
    margin-bottom: 1.5rem;
    font-size: 1rem;
}

.intro-features {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.feature-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.feature-text h4 {
    font-size: 1rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0.25rem;
}

.feature-text p {
    font-size: 0.85rem;
    color: rgba(255,255,255,0.7);
    margin: 0;
}

/* Skills Section */
.skills-section {
    padding: 120px 0;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-subtitle {
    font-size: 1.1rem;
    color: rgba(255,255,255,0.7);
    margin-top: 1rem;
    line-height: 1.6;
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.skill-card {
    background: rgba(255,255,255,0.05);
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 15px 40px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
    border: 1px solid rgba(255,107,53,0.2);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.skill-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
}

.skill-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 60px rgba(255,107,53,0.3);
    background: rgba(255,255,255,0.08);
}

.skill-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
}

.skill-image {
    position: relative;
    width: 100%;
    height: 200px;
    border-radius: 15px;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.skill-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.skill-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255,107,53,0.8) 0%, rgba(247,147,30,0.8) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.skill-card:hover .skill-image img {
    transform: scale(1.1);
}

.skill-card:hover .skill-overlay {
    opacity: 1;
}

.skill-card h3 {
    font-size: 1.3rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1rem;
}

.skill-card p {
    color: rgba(255,255,255,0.8);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
}

.skill-progress {
    width: 100%;
    height: 6px;
    background: rgba(255,107,53,0.1);
    border-radius: 3px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    border-radius: 3px;
    width: 0%;
    transition: width 2s ease-in-out;
}

/* Stats Section */
.stats-section {
    position: relative;
    padding: 120px 0;
    overflow: hidden;
}

.stats-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.stats-background img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: brightness(0.3);
}

.stats-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0,0,0,0.8) 0%, rgba(255,107,53,0.4) 100%);
    z-index: 2;
}

.stats-content {
    position: relative;
    z-index: 3;
}

.stats-header {
    text-align: center;
    margin-bottom: 4rem;
}

.stats-header h2 {
    font-size: 2.5rem;
    font-weight: 800;
    color: white;
    margin-bottom: 1rem;
}

.stats-header p {
    font-size: 1.1rem;
    color: rgba(255,255,255,0.8);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.stat-card {
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    padding: 2.5rem 2rem;
    border-radius: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-10px);
    background: rgba(255,255,255,0.15);
}

.stat-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.8rem;
    margin: 0 auto 1.5rem;
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    color: white;
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stat-label {
    font-size: 1rem;
    color: rgba(255,255,255,0.8);
    font-weight: 500;
}

/* Philosophy Section - Removed duplicate */

.philosophy-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.quote-section {
    position: relative;
    padding: 3rem;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.1);
}

.quote-decoration {
    position: absolute;
    top: -20px;
    left: -20px;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    border-radius: 50%;
    opacity: 0.1;
}

.quote-icon {
    font-size: 3rem;
    color: #ff6b35;
    margin-bottom: 1.5rem;
    opacity: 0.3;
}

.quote-content blockquote {
    font-size: 1.3rem;
    line-height: 1.7;
    color: #333;
    font-style: italic;
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.quote-content cite {
    font-size: 1rem;
    color: #ff6b35;
    font-weight: 600;
    font-style: normal;
}

.philosophy-title {
    font-size: 2rem;
    font-weight: 800;
    color: #ffffff;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #ff6b35 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.philosophy-text p {
    color: rgba(255,255,255,0.8);
    line-height: 1.8;
    margin-bottom: 1.5rem;
    font-size: 1rem;
}

.philosophy-principles {
    margin-top: 2rem;
}

.principle-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.principle-icon {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
    flex-shrink: 0;
}

.principle-text h4 {
    font-size: 1.1rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0.25rem;
}

.principle-text p {
    font-size: 0.9rem;
    color: rgba(255,255,255,0.7);
    margin: 0;
    line-height: 1.5;
}

/* Equipment Section */
.equipment-section {
    padding: 120px 0;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
}

.equipment-showcase {
    margin-top: 3rem;
}

.equipment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.equipment-card {
    background: rgba(255,255,255,0.05);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 40px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
    border: 1px solid rgba(255,107,53,0.2);
    backdrop-filter: blur(10px);
}

.equipment-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 60px rgba(255,107,53,0.3);
    background: rgba(255,255,255,0.08);
}

.equipment-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.equipment-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.equipment-card:hover .equipment-image img {
    transform: scale(1.1);
}

.equipment-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: rgba(255,107,53,0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    opacity: 0;
    transition: all 0.3s ease;
}

.equipment-card:hover .equipment-overlay {
    opacity: 1;
}

.equipment-content {
    padding: 2rem;
}

.equipment-content h4 {
    font-size: 1.3rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1rem;
}

.equipment-content p {
    color: rgba(255,255,255,0.8);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
}

.equipment-features {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.feature-tag {
    background: rgba(255,107,53,0.1);
    color: #ff6b35;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    border: 1px solid rgba(255,107,53,0.2);
}

/* About CTA Section */
.about-cta {
    position: relative;
    padding: 120px 0;
    overflow: hidden;
}

.cta-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.cta-background img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: brightness(0.3);
}

.cta-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0,0,0,0.8) 0%, rgba(255,107,53,0.4) 100%);
    z-index: 2;
}

.cta-content {
    position: relative;
    z-index: 3;
    text-align: center;
    color: white;
    max-width: 800px;
    margin: 0 auto;
}

.cta-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    margin: 0 auto 2rem;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 2.5rem;
    opacity: 0.9;
    line-height: 1.6;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.cta-buttons .btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 50px;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-primary {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(255,107,53,0.4);
}

.btn-secondary {
    background: transparent;
    color: white;
    border-color: rgba(255,255,255,0.3);
}

.btn-secondary:hover {
    background: rgba(255,255,255,0.1);
    transform: translateY(-3px);
}

.cta-contact-quick {
    display: flex;
    gap: 2rem;
    justify-content: center;
    flex-wrap: wrap;
}

.quick-contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255,255,255,0.8);
    font-size: 0.9rem;
}

.quick-contact-item i {
    color: #ff6b35;
    font-size: 1.1rem;
}

.quick-contact-item a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.quick-contact-item a:hover {
    color: #ff6b35;
}

/* Footer */
.footer {
    background: #1a1a1a;
    color: white;
    padding: 50px 0 20px;
    margin-top: 50px;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

/* Footer Brand */
.footer-brand h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.5rem;
}

.footer-brand p {
    color: #ff6b35;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.brand-desc {
    color: rgba(255, 255, 255, 0.8) !important;
    line-height: 1.6;
    margin-bottom: 1.5rem !important;
    font-size: 0.95rem !important;
    font-weight: 400 !important;
}

.social-links {
    display: flex;
    flex-direction: row;
    gap: 1rem;
    align-items: center;
    flex-wrap: nowrap;
    justify-content: flex-start;
}

.social-links a {
    width: 45px;
    height: 45px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 1.2rem;
    flex-shrink: 0;
    color: white;
}

.social-links a:nth-child(1) {
    background: linear-gradient(135deg, #e1306c, #fd1d1d);
}

.social-links a:nth-child(2) {
    background: linear-gradient(135deg, #1877f2, #42a5f5);
}

.social-links a:nth-child(3) {
    background: linear-gradient(135deg, #ff0000, #ff4444);
}

.social-links a:nth-child(4) {
    background: linear-gradient(135deg, #25d366, #128c7e);
}

.social-links a:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

/* Footer Sections */
.footer-links h4,
.footer-services h4,
.footer-contact h4 {
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    border-bottom: 2px solid #ff6b35;
    padding-bottom: 0.5rem;
    display: inline-block;
}

.footer-links ul,
.footer-services ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links ul li,
.footer-services ul li {
    margin-bottom: 0.5rem;
}

.footer-links ul li a,
.footer-services ul li a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
    font-size: 0.9rem;
}

.footer-links ul li a:hover,
.footer-services ul li a:hover {
    color: #ff6b35;
}

/* Footer Contact */
.contact-info p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.contact-info p i {
    color: #ff6b35;
    width: 16px;
}

/* Footer Bottom */
.footer-bottom {
    text-align: center;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.85rem;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .hero {
        flex-direction: column;
        text-align: center;
        padding: 100px 20px 50px;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .stats {
        justify-content: center;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .portfolio-grid {
        grid-template-columns: 1fr;
    }

    .pricing-grid {
        grid-template-columns: 1fr;
    }

    .pricing-card.featured {
        transform: none;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    /* About Page Responsive */
    .about-hero .hero-title {
        font-size: 3rem;
    }

    .about-hero .hero-subtitle {
        font-size: 1.2rem;
        margin-bottom: 3rem;
    }

    .philosophy-section {
        padding: 80px 0;
    }

    .philosophy-section .section-title {
        font-size: 2.8rem;
    }

    .philosophy-section .section-subtitle {
        font-size: 1.1rem;
    }

    .quote-text {
        font-size: 1.5rem;
    }

    .quote-box {
        padding: 30px;
    }

    .philosophy-text-content {
        padding-right: 0;
        margin-bottom: 50px;
    }

    .philosophy-image-container {
        padding-left: 0;
    }

    .content-title {
        font-size: 1.8rem;
    }

    .content-description {
        font-size: 1rem;
    }

    .principle-card {
        padding: 25px;
        gap: 20px;
    }

    .principle-number {
        font-size: 2rem;
        min-width: 50px;
    }

    .principle-info h4 {
        font-size: 1.2rem;
    }

    .philosophy-img {
        height: 350px;
    }

    .philosophy-section .stats-cards {
        flex-direction: column !important;
        gap: 15px !important;
    }
}

@media (max-width: 480px) {
    .about-hero .hero-title {
        font-size: 2.5rem;
        letter-spacing: -1px;
    }

    .about-hero .hero-subtitle {
        font-size: 1rem;
    }

    .philosophy-section {
        padding: 60px 0;
    }

    .philosophy-section .section-title {
        font-size: 2.2rem;
    }

    .philosophy-section .section-subtitle {
        font-size: 1rem;
    }

    .quote-text {
        font-size: 1.3rem;
    }

    .quote-box {
        padding: 25px;
    }

    .content-title {
        font-size: 1.6rem;
    }

    .content-description {
        font-size: 0.95rem;
    }

    .principle-card {
        padding: 20px;
        gap: 15px;
    }

    .principle-number {
        font-size: 1.8rem;
        min-width: 40px;
    }

    .principle-info h4 {
        font-size: 1.1rem;
    }

    .philosophy-img {
        height: 250px;
    }

    .philosophy-section .stat-card {
        padding: 20px !important;
        gap: 12px !important;
    }

    .philosophy-section .stat-icon {
        width: 40px !important;
        height: 40px !important;
        font-size: 1.1rem !important;
    }

    .philosophy-section .stat-number {
        font-size: 1.5rem !important;
    }

    .philosophy-section .stat-label {
        font-size: 0.8rem !important;
    }

    /* Nature Gallery Responsive */
    .nature-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
    }

    .nature-item.large {
        grid-column: span 1;
        height: 300px;
    }

    .nature-item:not(.large) {
        height: 250px;
    }

    .overlay-content h3 {
        font-size: 1.5rem;
    }

    .nature-overlay {
        padding: 20px;
    }

    /* Nature Header Responsive */
    .nature-header {
        height: 70vh;
        min-height: 500px;
    }

    .nature-header h1 {
        font-size: 2.5rem;
    }

    .nature-header p {
        font-size: 1.1rem;
    }

    .header-stats {
        flex-direction: column;
        gap: 20px;
        align-items: center;
    }

    .header-stats .stat-item {
        padding: 15px 30px;
    }

    .header-stats .stat-number {
        font-size: 2rem;
    }
}
