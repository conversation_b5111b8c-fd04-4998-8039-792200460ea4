<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Photography - Abhi Photography</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="logo.svg" alt="Abhi Photography" class="logo-img">
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="about.html" class="nav-link">About</a>
                </li>
                <li class="nav-item dropdown">
                    <a href="portfolio.html" class="nav-link active">Portfolio <i class="fas fa-chevron-down"></i></a>
                    <ul class="dropdown-menu">
                        <li><a href="weddings.html">Weddings</a></li>
                        <li><a href="nature.html">Nature/Landscape</a></li>
                        <li><a href="portraits.html">Portraits</a></li>
                        <li><a href="events.html">Events</a></li>
                        <li><a href="products.html">Product Photography</a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a href="contact.html" class="nav-link">Contact</a>
                </li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header product-header">
        <div class="header-background">
            <img src="https://images.pexels.com/photos/1667088/pexels-photo-1667088.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&dpr=1"
                 alt="Product Photography Background"
                 onerror="this.onerror=null; this.src='https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=1920&h=1080&fit=crop'; if(this.onerror) this.src='https://picsum.photos/1920/1080?random=20';">
            <div class="header-overlay"></div>
        </div>
        <div class="container">
            <div class="header-content">
                <span class="header-badge">
                    <i class="fas fa-camera-retro"></i>
                    Product Photography
                </span>
                <h1>Product Photography</h1>
                <p>Professional product photography that showcases your items in the best possible light, driving sales and enhancing your brand presence</p>
                <div class="header-stats">
                    <div class="stat-item">
                        <span class="stat-number">300+</span>
                        <span class="stat-label">Products Shot</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">50+</span>
                        <span class="stat-label">Brands Served</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">5+</span>
                        <span class="stat-label">Years Experience</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Product Gallery -->
    <section class="product-gallery">
        <div class="container">
            <div class="gallery-intro">
                <div class="intro-content">
                    <span class="section-badge">
                        <i class="fas fa-camera"></i>
                        Gallery
                    </span>
                    <h2>Bringing Products to Life</h2>
                    <p>High-quality product photography that highlights the details, craftsmanship, and appeal of your products for e-commerce, marketing, and promotional use. Every shot is carefully crafted to maximize visual impact and drive sales.</p>
                    <div class="intro-features">
                        <div class="feature-item">
                            <i class="fas fa-shopping-cart"></i>
                            <span>E-commerce Ready</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-palette"></i>
                            <span>Lifestyle Shots</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-search-plus"></i>
                            <span>Detail Photography</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-cube"></i>
                            <span>360° Views</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="product-grid">
                <div class="product-item large">
                    <img src="https://images.pexels.com/photos/1667088/pexels-photo-1667088.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&dpr=1"
                         alt="Product Photography"
                         onerror="this.onerror=null; this.src='https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop'; if(this.onerror) this.src='https://picsum.photos/800/600?random=21';">
                    <div class="product-overlay">
                        <div class="overlay-content">
                            <h3>E-commerce Products</h3>
                            <p>Professional Product Shots</p>
                            <span class="product-date">2024</span>
                        </div>
                    </div>
                </div>

                <div class="product-item">
                    <img src="https://images.pexels.com/photos/1927259/pexels-photo-1927259.jpeg?auto=compress&cs=tinysrgb&w=500&h=400&dpr=1"
                         alt="Jewelry Photography"
                         onerror="this.onerror=null; this.src='https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=500&h=400&fit=crop'; if(this.onerror) this.src='https://picsum.photos/500/400?random=22';">
                    <div class="product-overlay">
                        <div class="overlay-content">
                            <h3>Jewelry Photography</h3>
                            <p>Luxury Items</p>
                            <span class="product-date">2024</span>
                        </div>
                    </div>
                </div>

                <div class="product-item">
                    <img src="https://images.pexels.com/photos/1598505/pexels-photo-1598505.jpeg?auto=compress&cs=tinysrgb&w=500&h=400&dpr=1"
                         alt="Fashion Products"
                         onerror="this.onerror=null; this.src='https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=500&h=400&fit=crop'; if(this.onerror) this.src='https://picsum.photos/500/400?random=23';">
                    <div class="product-overlay">
                        <div class="overlay-content">
                            <h3>Fashion Products</h3>
                            <p>Clothing & Accessories</p>
                            <span class="product-date">2024</span>
                        </div>
                    </div>
                </div>
                
                <div class="product-item">
                    <img src="https://images.pexels.com/photos/788946/pexels-photo-788946.jpeg?auto=compress&cs=tinysrgb&w=500&h=400&dpr=1"
                         alt="Electronics"
                         onerror="this.onerror=null; this.src='https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500&h=400&fit=crop'; if(this.onerror) this.src='https://picsum.photos/500/400?random=24';">
                    <div class="product-overlay">
                        <div class="overlay-content">
                            <h3>Electronics</h3>
                            <p>Tech Products</p>
                            <span class="product-date">2024</span>
                        </div>
                    </div>
                </div>

                <div class="product-item">
                    <img src="https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?auto=compress&cs=tinysrgb&w=500&h=400&dpr=1"
                         alt="Food Products"
                         onerror="this.onerror=null; this.src='https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=500&h=400&fit=crop'; if(this.onerror) this.src='https://picsum.photos/500/400?random=25';">
                    <div class="product-overlay">
                        <div class="overlay-content">
                            <h3>Food Products</h3>
                            <p>Culinary Photography</p>
                            <span class="product-date">2024</span>
                        </div>
                    </div>
                </div>

                <div class="product-item">
                    <img src="https://images.pexels.com/photos/2113855/pexels-photo-2113855.jpeg?auto=compress&cs=tinysrgb&w=500&h=400&dpr=1"
                         alt="Cosmetics"
                         onerror="this.onerror=null; this.src='https://images.unsplash.com/photo-1571781926291-c477ebfd024b?w=500&h=400&fit=crop'; if(this.onerror) this.src='https://picsum.photos/500/400?random=26';">
                    <div class="product-overlay">
                        <div class="overlay-content">
                            <h3>Cosmetics</h3>
                            <p>Beauty Products</p>
                            <span class="product-date">2024</span>
                        </div>
                    </div>
                </div>

                <div class="product-item">
                    <img src="https://images.pexels.com/photos/1350789/pexels-photo-1350789.jpeg?auto=compress&cs=tinysrgb&w=500&h=400&dpr=1"
                         alt="Lifestyle Products"
                         onerror="this.onerror=null; this.src='https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=500&h=400&fit=crop'; if(this.onerror) this.src='https://picsum.photos/500/400?random=27';">
                    <div class="product-overlay">
                        <div class="overlay-content">
                            <h3>Lifestyle Products</h3>
                            <p>Home & Living</p>
                            <span class="product-date">2024</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Product Services -->
    <section class="product-services">
        <div class="container">
            <div class="section-header">
                <h2>Product Photography Services</h2>
                <p>Comprehensive product photography solutions for your business</p>
            </div>
            
            <div class="services-grid">
                <div class="service-item">
                    <div class="service-image">
                        <img src="https://images.pexels.com/photos/1667088/pexels-photo-1667088.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&dpr=1"
                             alt="E-commerce Photography"
                             onerror="this.onerror=null; this.src='https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600&h=400&fit=crop'; if(this.onerror) this.src='https://picsum.photos/600/400?random=28';">
                        <div class="service-overlay">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                    </div>
                    <div class="service-content">
                        <h3>E-commerce Photography</h3>
                        <p>Clean, professional product shots perfect for online stores and marketplaces.</p>
                    </div>
                </div>

                <div class="service-item">
                    <div class="service-image">
                        <img src="https://images.pexels.com/photos/1350789/pexels-photo-1350789.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&dpr=1"
                             alt="Lifestyle Photography"
                             onerror="this.onerror=null; this.src='https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600&h=400&fit=crop'; if(this.onerror) this.src='https://picsum.photos/600/400?random=29';">
                        <div class="service-overlay">
                            <i class="fas fa-palette"></i>
                        </div>
                    </div>
                    <div class="service-content">
                        <h3>Lifestyle Photography</h3>
                        <p>Products in real-world settings to show how they fit into customers' lives.</p>
                    </div>
                </div>

                <div class="service-item">
                    <div class="service-image">
                        <img src="https://images.pexels.com/photos/1927259/pexels-photo-1927259.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&dpr=1"
                             alt="Detail Shots"
                             onerror="this.onerror=null; this.src='https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=600&h=400&fit=crop'; if(this.onerror) this.src='https://picsum.photos/600/400?random=30';">
                        <div class="service-overlay">
                            <i class="fas fa-search-plus"></i>
                        </div>
                    </div>
                    <div class="service-content">
                        <h3>Detail Shots</h3>
                        <p>Close-up photography highlighting textures, materials, and craftsmanship.</p>
                    </div>
                </div>

                <div class="service-item">
                    <div class="service-image">
                        <img src="https://images.pexels.com/photos/788946/pexels-photo-788946.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&dpr=1"
                             alt="360° Photography"
                             onerror="this.onerror=null; this.src='https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=600&h=400&fit=crop'; if(this.onerror) this.src='https://picsum.photos/600/400?random=31';">
                        <div class="service-overlay">
                            <i class="fas fa-cube"></i>
                        </div>
                    </div>
                    <div class="service-content">
                        <h3>360° Photography</h3>
                        <p>Interactive product views that let customers see every angle.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials -->
    <section class="testimonials">
        <div class="container">
            <div class="testimonial-content">
                <blockquote>
                    "Abhi's product photography significantly improved our online sales. The images are crisp, professional, and really showcase our products beautifully. Highly recommended for e-commerce businesses!"
                </blockquote>
                <div class="testimonial-author">
                    <div class="author-info">
                        <h4>Amit Patel</h4>
                        <p>E-commerce Business - 2024</p>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <div class="cta-content">
                <h2>Ready to Showcase Your Products?</h2>
                <p>Let's create stunning product photography that drives sales and showcases your brand.</p>
                <a href="contact.html" class="cta-btn">Get Started</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Abhi Photography</h3>
                    <p>Capturing life's beautiful moments with passion and artistry.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="about.html">About</a></li>
                        <li><a href="portfolio.html">Portfolio</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Services</h4>
                    <ul>
                        <li><a href="weddings.html">Wedding Photography</a></li>
                        <li><a href="portraits.html">Portrait Photography</a></li>
                        <li><a href="events.html">Event Photography</a></li>
                        <li><a href="products.html">Product Photography</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact Info</h4>
                    <p><i class="fas fa-phone"></i> +91 7775920627</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-map-marker-alt"></i> Rajmachi Point, Battryhill, Khandala 410301</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Abhi Photography. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
